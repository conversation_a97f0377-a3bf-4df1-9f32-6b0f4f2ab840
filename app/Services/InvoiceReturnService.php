<?php

namespace App\Services;

use App\Actions\Application\Web\ZatcaInvoices\ZatcaInvoiceAction;
use App\Enums\Application\InvoiceTypeEnum;
use App\Enums\Application\InvoiceTypePerformedEnum;
use App\Enums\Application\ProductTypeEnum;
use App\Enums\Application\Zatca\ZatcaReturnInvoiceReasons;
use App\Helpers\Helper;
use App\Models\MerchantInvoice;
use App\Models\MerchantProductBranchQuantity;
use App\Models\MerchantSaleInvoiceDiscount;
use App\Models\MerchantSaleInvoicePaymentMethod;
use App\Models\MerchantSaleInvoiceProduct;
use App\Models\MerchantsProduct;
use App\Models\MerchantsSetting;
use App\Models\MerchantSaleInvoicePromotionGroup;
use BCMathExtended\BC;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Salla\ZATCA\GenerateQrCode;
use Salla\ZATCA\Tags\InvoiceDate;
use Salla\ZATCA\Tags\InvoiceTaxAmount;
use Salla\ZATCA\Tags\InvoiceTotalAmount;
use Salla\ZATCA\Tags\Seller;
use Salla\ZATCA\Tags\TaxNumber;

class InvoiceReturnService
{
    /**
     * Process an invoice return
     *
     * @param int $invoiceId The ID of the original invoice
     * @param Request|array $data The request or array containing return data
     * @param object $user The current user
     * @return array|null Returns an array with returnedInvoiceId on success, null on failure
     * @throws Exception
     */
    public function processReturn(int $invoiceId, $data, $user)
    {
        $returnedItems = [];
        $branchIds = $user->branches->pluck('branch_id')->toArray();

        // Convert to array if Request object
        $requestData = $data instanceof Request ? $data->all() : $data;

        // Check permissions
        if (!($user->group->permissions['invoices_return'] || $user->main_account) ||
            !(in_array($requestData['branch'], $branchIds) || $user->main_account)) {
            return null;
        }

        $originalInvoice = MerchantInvoice::where('id', $invoiceId)->first();

        // Get all products from the original invoice and any related return invoices
        $allInvoiceProducts = MerchantSaleInvoiceProduct::where('invoice_id', $invoiceId)
            ->orWhere('related_invoice', $invoiceId)
            ->get();

        // Calculate remaining returnable quantities
        $remainingQuantities = [];
        foreach ($allInvoiceProducts as $product) {
            // Use consistent cart ID construction logic
            $productKey = $product->product_id; // Start with product_id

            // Add variant_id if it's a variant product
            if (isset($product->is_variant) && $product->is_variant && $product->product_variant_id) {
                $productKey .= '-' . $product->product_variant_id;
            }

            // Add promotion_id if it has promotion
            if (isset($product->has_promotion) && $product->has_promotion && $product->promotion_id) {
                $productKey .= '-' . $product->promotion_id;
            }

            if (array_key_exists($productKey, $remainingQuantities)) {
                if ($product->invoice_type === InvoiceTypeEnum::RETURN->value) {
                    // Subtract returned quantities
                    $remainingQuantities[$productKey] = BC::sub($remainingQuantities[$productKey], $product->cart_quantity, 10);
                } else {
                    // Add original quantities for returnable products
                    if ($product->returnable) {
                        $remainingQuantities[$productKey] = BC::add($remainingQuantities[$productKey], $product->cart_quantity, 10);
                    }
                }
            } else {
                if ($product->invoice_type === InvoiceTypeEnum::RETURN->value) {
                    // Initialize with negative value for returns
                    $remainingQuantities[$productKey] = BC::sub(0, $product->cart_quantity, 10);
                } else {
                    // Initialize with original quantity for returnable products
                    if ($product->returnable) {
                        $remainingQuantities[$productKey] = $product->cart_quantity;
                    }
                }
            }
        }
        // Process quantities from request and validate against remaining quantities
        $returnedItems = [];

        // Check if we have the old format (quantity key) or new format (products key)
        if (isset($requestData['quantity'])) {
            // Old format - direct quantity mapping
            foreach ($requestData['quantity'] as $key => $quantity) {
                $quantity = intval($quantity);
                if ($quantity > 0) {
                    // Get the product key in the same format as remainingQuantities
                    $productKey = $key;

                    // Ensure we don't exceed the remaining returnable quantity
                    if (isset($remainingQuantities[$productKey]) && $remainingQuantities[$productKey] > 0) {
                        $maxQuantity = $remainingQuantities[$productKey];
                        $returnedItems[$key] = min($quantity, $maxQuantity);
                    }
                }
            }
        } elseif (isset($requestData['products'])) {
            // New format - products with quantity inside
            foreach ($requestData['products'] as $productId => $productData) {
                if (isset($productData['quantity'])) {
                    $quantity = intval($productData['quantity']);
                    if ($quantity > 0) {
                        // For variant products, use a composite key
                        $productKey = isset($productData['variant_id']) ?
                            $productId . '-' . $productData['variant_id'] :
                            $productId;

                        // Ensure we don't exceed the remaining returnable quantity
                        if (isset($remainingQuantities[$productKey]) && $remainingQuantities[$productKey] > 0) {
                            $maxQuantity = $remainingQuantities[$productKey];
                            $returnedItems[$productKey] = min($quantity, $maxQuantity);
                        }
                    }
                }
            }
        }

        // If no valid items to return, return early
        if (empty($returnedItems)) {
            return ['redirect' => "/invoices/return/{$invoiceId}"];
        }

        $products = MerchantSaleInvoiceProduct::where(function ($query) use ($returnedItems, $invoiceId) {
            $query->where('invoice_id', $invoiceId);

            $query->where(function ($subQuery) use ($returnedItems) {
                foreach (array_keys($returnedItems) as $key) {
                    if (strpos($key, '-') !== false) {
                        // Parse cart ID: productId[-variantId][-promotionId]
                        $parts = explode('-', $key);
                        $productId = $parts[0];

                        if (count($parts) == 2) {
                            $secondPart = $parts[1];

                            $subQuery->orWhere(function ($innerQuery) use ($productId, $secondPart) {
                                $innerQuery->where('product_id', $productId)
                                    ->orWhere(function ($q) use ($secondPart) {
                                        $q->where('product_variant_id', $secondPart);
                                    });
                            });
                        } elseif (count($parts) == 3) {
                            // Three parts: productId-variantId-promotionId
                            $variantId = $parts[1];

                            $subQuery->orWhere(function ($innerQuery) use ($productId, $variantId) {
                                $innerQuery->where('product_id', $productId)
                                    ->where('product_variant_id', $variantId);
                            });
                        }
                    } else {
                        // Regular product key (just productId)
                        $subQuery->orWhere('product_id', $key);
                    }
                }
            });
        })->get();

        $totalAmountReturnedTaxExclusive = 0;
        $totalTaxReturned = 0;
        $totalCost = 0;
        $totalCostTaxExclusive = 0;
        $insertProducts = array();
        $totalQuantity = 0;

        // Add returned quantity to each product
        $productsWithMoreInformation = $products->map(function ($item) use ($returnedItems) {
            $productKey = $item->product_id; // Start with product_id

            // Add variant_id if it's a variant product
            if (isset($item->is_variant) && $item->is_variant && $item->product_variant_id) {
                $productKey .= '-' . $item->product_variant_id;
            }

            // Add promotion_id if it has promotion
            if (isset($item->has_promotion) && $item->has_promotion && $item->promotion_id) {
                $productKey .= '-' . $item->promotion_id;
            }

            $item['returned_quantity'] = $returnedItems[$productKey] ?? 0;
            return $item;
        })->filter(function ($item) {
            return $item['returned_quantity'] > 0;
        });

        // Validate promotion group returns after filtering products
        // Only validate if there are products being returned that are part of promotion groups
        try {
            $promotionError = $this->validatePromotionGroupReturns($originalInvoice, $returnedItems);
            if ($promotionError) {
                return ['error' => $promotionError];
            }
        } catch (\Exception $e) {
            return ['error' => 'خطأ في التحقق من مجموعات العروض: ' . $e->getMessage()];
        }

        // Get original invoice discount information if it exists
        $originalInvoiceDiscount = null;
        if (!is_null($originalInvoice->is_discounted) && $originalInvoice->is_discounted) {
            $originalInvoiceDiscount = MerchantSaleInvoiceDiscount::where('invoice_id', $invoiceId)->first();
        }

        // Validate and calculate totals
        foreach ($productsWithMoreInformation as $productWithMoreInformation) {
            // Check if product is returnable
            if ($productWithMoreInformation['returnable'] != 1) {
                return ['error' => "This action is not permitted, you cannot return unreturnable items"];
            }

            // Calculate costs (not affected by promotions)
            $totalCost = BC::add(
                $totalCost,
                BC::mul($productWithMoreInformation['returned_quantity'], $productWithMoreInformation['cost_tax_inclusive'], 10),
                10
            );

            $totalCostTaxExclusive = BC::add(
                $totalCostTaxExclusive,
                BC::mul($productWithMoreInformation['returned_quantity'], $productWithMoreInformation['cost_tax_exclusive'], 10),
                10
            );

            // Calculate individual product discount if original invoice was discounted
            $productDiscountTaxExclusive = 0;
            $productDiscountTax = 0;
            $productRowInvoiceDiscount = 0;
            $productRowInvoiceDiscountVat = 0;

            if ($originalInvoiceDiscount) {
                // Calculate this product's proportion of the original invoice total using RETURNED quantity
                // This directly calculates the proportional discount based on what's actually being returned
                $productTotal = BC::mul($productWithMoreInformation['returned_quantity'], $productWithMoreInformation['price_tax_inclusive'], 10);
                $proportion = BC::div($productTotal, $originalInvoice->total_tax_inclusive, 10);

                // Use actual invoice discount amounts (proportional to returned quantity)
                $productDiscountTaxExclusive = BC::mul($proportion, $originalInvoiceDiscount->total_discount_tax_exclusive, 10);
                $productDiscountTax = BC::mul($proportion, $originalInvoiceDiscount->total_discount_tax, 10);

                // Calculate proportional row invoice discount (from row_invoice_discount fields)
                // Only apply return ratio to row-level discounts since invoice-level is already proportional
                if (isset($productWithMoreInformation['row_invoice_discount']) && $productWithMoreInformation['row_invoice_discount'] > 0) {
                    $returnRatio = BC::div($productWithMoreInformation['returned_quantity'], $productWithMoreInformation['cart_quantity'], 10);
                    $productRowInvoiceDiscount = BC::mul($productWithMoreInformation['row_invoice_discount'], $returnRatio, 10);

                    if (isset($productWithMoreInformation['row_invoice_discount_vat'])) {
                        $productRowInvoiceDiscountVat = BC::mul($productWithMoreInformation['row_invoice_discount_vat'], $returnRatio, 10);
                    }
                }
            }

            // Calculate all required fields for return invoice product (following PosCart pattern)
            $returnQuantity = $productWithMoreInformation["returned_quantity"];
            $unitPriceTaxInclusive = $productWithMoreInformation['price_tax_inclusive'];
            $unitPriceTaxExclusive = $productWithMoreInformation["price_tax_exclusive"];
            $unitCostTaxInclusive = $productWithMoreInformation["cost_tax_inclusive"];
            $unitCostTaxExclusive = $productWithMoreInformation["cost_tax_exclusive"];

            // Step 1: Calculate subtotal from original price × quantity (like PosCart)
            $subtotal = BC::mul($unitPriceTaxExclusive, $returnQuantity, 10);
            $subtotalRounded = BC::round($subtotal, 2);

            // Step 2: Calculate promotion discount based on original product
            // For returns, we need to preserve the original promotion discount amounts
            $discount = '0';
            $discountRounded = '0';

            // Check if the original product had promotion discounts
            if (isset($productWithMoreInformation['discount']) && $productWithMoreInformation['discount'] > 0) {
                // Calculate proportional discount based on returned quantity
                $returnRatio = BC::div($productWithMoreInformation['returned_quantity'], $productWithMoreInformation['cart_quantity'], 10);
                $discount = BC::mul($productWithMoreInformation['discount'], $returnRatio, 10);
                $discountRounded = BC::round($discount, 2);
            }

            // Step 3: Calculate subtotal after discount (subtotal - promotion discount)
            $subtotalAfterDiscount = BC::sub($subtotal, $discount, 10);
            $subtotalAfterDiscountRounded = BC::round($subtotalAfterDiscount, 2);

            // Step 4: Calculate tax on the discounted amount (like PosCart)
            $taxPercentage = $productWithMoreInformation['tax_percentage'] ?? 0;
            $tax = BC::mul($subtotalAfterDiscount, BC::div($taxPercentage, 100, 10), 10);
            $taxRounded = BC::round($tax, 2);

            // Step 5: Calculate total = subtotal after discount + tax (like PosCart)
            // Do NOT subtract invoice discount here - that's applied at invoice level
            $total = BC::add($subtotalAfterDiscount, $tax, 10);
            $totalRounded = BC::round($total, 2);

            // Calculate rounded values for prices
            $unitPriceTaxExclusiveRounded = isset($productWithMoreInformation["price_tax_exclusive_rounded"])
                ? $productWithMoreInformation["price_tax_exclusive_rounded"]
                : BC::round($unitPriceTaxExclusive, 2);

            // Prepare product data for insertion
            $insertProducts[] = [
                "related_invoice" => $invoiceId,
                "product_id" => $productWithMoreInformation["product_id"],
                "product_name_en" => $productWithMoreInformation["product_name_en"],
                "product_name_ar" => $productWithMoreInformation["product_name_ar"],
                "barcode" => $productWithMoreInformation["barcode"] ?? '',
                // Positive prices for returns (as they represent the value being returned)
                "price_tax_inclusive" => $unitPriceTaxInclusive,
                "price_tax_exclusive" => $unitPriceTaxExclusive,
                "price_tax_exclusive_rounded" => $unitPriceTaxExclusiveRounded,
                "cost_tax_exclusive" => $unitCostTaxExclusive,
                "cost_tax_inclusive" => $unitCostTaxInclusive,
                // Calculated fields following PosCart pattern
                "subtotal" => $subtotal,
                "subtotal_rounded" => $subtotalRounded,
                "subtotal_after_discount" => $subtotalAfterDiscount,
                "subtotal_after_discount_rounded" => $subtotalAfterDiscountRounded,
                "total" => $total,
                "total_rounded" => $totalRounded,
                "vat" => $tax,
                "vat_rounded" => $taxRounded,
                "type" => $productWithMoreInformation["type"],
                "cart_quantity" => $returnQuantity,
                "unit_id" => $productWithMoreInformation['unit_id'],
                "unit_name" => $productWithMoreInformation['unit_name'],
                "tax_code" => $productWithMoreInformation["tax_code"],
                "tax_schema" => $productWithMoreInformation["tax_schema"],
                "tax_type" => $productWithMoreInformation["tax_type"],
                "created_at" => date("Y-m-d H:i:s"),
                "updated_at" => date("Y-m-d H:i:s"),
                "invoice_type" => InvoiceTypeEnum::RETURN->value,
                "invoice_type_performed" => InvoiceTypePerformedEnum::RETURN->value,
                'branch_id' => $requestData['branch'],
                'registery_id' => $productWithMoreInformation['registery_id'],
                'customer_id' => $productWithMoreInformation['customer_id'],
                'wholesale_status' => $productWithMoreInformation['wholesale_status'],
                'returnable' => $productWithMoreInformation['returnable'],
                'is_variant' => $productWithMoreInformation['is_variant'] ?? false,
                'product_variant_id' => $productWithMoreInformation['product_variant_id'] ?? null,
                'cart_id' => $productWithMoreInformation['cart_id'] ?? $productWithMoreInformation['product_id'],
                'tax_percentage' => $productWithMoreInformation['tax_percentage'] ?? 0,
                'has_promotion' => $productWithMoreInformation['has_promotion'] ?? 0,
                'promotion_id' => $productWithMoreInformation['promotion_id'] ?? null,
                'discount' => $discount,
                'discount_rounded' => $discountRounded,
                'discount_tax_exclusive' => $productDiscountTaxExclusive,
                'discount_tax' => $productDiscountTax,
                'row_invoice_discount' => $productRowInvoiceDiscount,
                'row_invoice_discount_rounded' => BC::round($productRowInvoiceDiscount, 2),
                'row_invoice_discount_vat' => $productRowInvoiceDiscountVat,
                'row_invoice_discount_vat_rounded' => BC::round($productRowInvoiceDiscountVat, 2),
            ];

            $totalQuantity = BC::add($totalQuantity, $productWithMoreInformation['returned_quantity'], 10);

            // Calculate invoice totals from individual product totals (includes promotion discounts)
            $totalAmountReturnedTaxExclusive = BC::add($totalAmountReturnedTaxExclusive, $subtotalAfterDiscount, 10);
            $totalTaxReturned = BC::add($totalTaxReturned, $tax, 10);
        }

        // Check if there are any items to return
        if ($totalQuantity <= 0) {
            return ['redirect' => "/invoices/return/{$invoiceId}"];
        }

        // Handle rounding if needed
        if ($originalInvoice['is_rounded']) {
            // We round here because when it was sold, it was rounded
            $totalAmountReturned = BC::round(BC::add($totalAmountReturnedTaxExclusive, $totalTaxReturned, 10));
            $taxPercentage = Helper::calculateTaxPercentage($totalAmountReturnedTaxExclusive, $totalTaxReturned);
            $subtotal = ($taxPercentage == 0) ? 0 : BC::round(Helper::excludeTax($totalAmountReturned, $taxPercentage), 2);
            $totalTaxReturned = BC::sub($totalAmountReturned, $subtotal, 10);
        } else {
            $totalAmountReturned = BC::add($totalAmountReturnedTaxExclusive, $totalTaxReturned, 10);
        }

        // Get settings for ZATCA
        $settings = MerchantsSetting::query()->first();
        $zatca_date = date("Y-m-d\TH:i:s\Z");

        // Generate QR code for ZATCA
        $qr_zatca = GenerateQrCode::fromArray([
            new Seller($settings["name"]),
            new TaxNumber($settings["VAT"]),
            new InvoiceDate($zatca_date),
            new InvoiceTotalAmount($totalAmountReturned),
            new InvoiceTaxAmount($totalTaxReturned)
        ])->toBase64();

        $qr_zatca_image = GenerateQrCode::fromArray([
            new Seller($settings["name"]),
            new TaxNumber($settings["VAT"]),
            new InvoiceDate($zatca_date),
            new InvoiceTotalAmount($totalAmountReturned),
            new InvoiceTaxAmount($totalTaxReturned)
        ])->render();

        // Generate pretty ID for the return invoice
        $returnInvoiceCount = MerchantInvoice::where([
            'related_invoice' => $invoiceId,
            'invoice_type' => InvoiceTypeEnum::RETURN->value
        ])->count();
        $returnInvoiceCount++;

        // Prepare invoice data
        if (!is_null($originalInvoice->is_discounted) && $originalInvoice->is_discounted) {
            $invoice = $this->prepareDiscountedInvoiceData(
                $originalInvoice,
                $zatca_date,
                $qr_zatca,
                $qr_zatca_image,
                $requestData['branch'],
                $productsWithMoreInformation,
                $returnInvoiceCount,
                $invoiceId,
                $insertProducts,
                $user
            );
        } else {
            $invoice = $this->prepareRegularInvoiceData(
                $totalAmountReturned,
                $totalTaxReturned,
                $totalCost,
                $totalCostTaxExclusive,
                $zatca_date,
                $qr_zatca,
                $qr_zatca_image,
                $requestData['branch'],
                $productsWithMoreInformation,
                $returnInvoiceCount,
                $invoiceId,
                $insertProducts,
                $originalInvoice,
                $user
            );
        }

        $returnedInvoiceId = 0;

        try {
            DB::transaction(function () use (
                $insertProducts,
                $invoice,
                &$returnedInvoiceId,
                &$totalAmountReturned,
                &$requestData,
                &$user,
                &$originalInvoice
            ) {
                $invoice = MerchantInvoice::create($invoice);
                $invoice->refresh();

                foreach ($insertProducts as $key => $product) {
                    $insertProducts[$key]["invoice_id"] = $invoice->id;

                    // Skip quantity updates for service products
                    if ($product['type'] !== ProductTypeEnum::SERVICE->value) {
                        $this->updateInventoryQuantities($product);
                    }
                }

                // Handle discounts if needed
                if ($invoice['is_discounted']) {
                    $this->createReturnDiscount($invoice->id, $originalInvoice, $insertProducts);
                }

                // Create invoice products
                $invoice->salesProducts()->createMany($insertProducts);

                // Create payment method
                $returnedInvoiceId = $invoice->id;
                $this->createReturnPaymentMethod($returnedInvoiceId, $totalAmountReturned, $requestData['payment']);

                // Process ZATCA if enabled
                if ($user->isZatcaEnabled) {
                    app(ZatcaInvoiceAction::class)($invoice);
                }
            });
        } catch (\Exception $exception) {
            Log::debug($exception);
            if ($user->isZatcaEnabled) {
                return ['error' => __('messages.zatca_unavailable')];
            } else {
                return ['error' => __('messages.invoice_error')];
            }
        }

        // Determine redirect URL
        if (isset($requestData['backto']) && $requestData['backto'] == 'pos') {
            return [
                'redirect' => "pos/{$requestData['branchId']}/{$requestData['registeryId']}/?return=true",
                'returnedInvoiceId' => $returnedInvoiceId
            ];
        } else {
            return [
                'redirect' => "/invoices/display/{$invoiceId}?return=true",
                'returnedInvoiceId' => $returnedInvoiceId
            ];
        }
    }

    /**
     * Prepare invoice data for a discounted invoice
     */
    private function prepareDiscountedInvoiceData($originalInvoice, $zatca_date, $qr_zatca, $qr_zatca_image, $branch, $productsWithMoreInformation, $returnInvoiceCount, $invoiceId, $insertProducts, $user)
    {
        // Calculate actual returned amounts from insertProducts
        $totalReturnedCost = '0';
        $totalReturnedCostTaxExclusive = '0';
        $totalReturnedDiscountTaxExclusive = '0';
        $totalReturnedDiscountTax = '0';
        $totalBeforeInvoiceDiscount = '0';
        $totalTax = '0';

        foreach ($insertProducts as $product) {
            // Sum product totals (these include promotion discounts but NOT invoice discounts)
            $totalBeforeInvoiceDiscount = BC::add($totalBeforeInvoiceDiscount, $product['total'], 10);
            $totalTax = BC::add($totalTax, $product['vat'], 10);

            // Add cost calculations if available
            if (isset($product['cost_tax_inclusive'])) {
                $productCostTotal = BC::mul($product['cost_tax_inclusive'], $product['cart_quantity'], 10);
                $totalReturnedCost = BC::add($totalReturnedCost, $productCostTotal, 10);
            }

            if (isset($product['cost_tax_exclusive'])) {
                $productCostTaxExclusiveTotal = BC::mul($product['cost_tax_exclusive'], $product['cart_quantity'], 10);
                $totalReturnedCostTaxExclusive = BC::add($totalReturnedCostTaxExclusive, $productCostTaxExclusiveTotal, 10);
            }

            // Sum invoice-level discounts to apply at invoice level
            if (isset($product['row_invoice_discount']) && $product['row_invoice_discount'] > 0) {
                $totalReturnedDiscountTaxExclusive = BC::add($totalReturnedDiscountTaxExclusive, $product['row_invoice_discount'], 10);
            }

            if (isset($product['row_invoice_discount_vat']) && $product['row_invoice_discount_vat'] > 0) {
                $totalReturnedDiscountTax = BC::add($totalReturnedDiscountTax, $product['row_invoice_discount_vat'], 10);
                // Add row_invoice_discount_vat to total tax (this is the tax component of invoice discount)
                $totalTax = BC::add($totalTax, $product['row_invoice_discount_vat'], 10);
            }
        }

        // Apply invoice-level discounts to the total
        $totalInvoiceDiscount = BC::add($totalReturnedDiscountTaxExclusive, $totalReturnedDiscountTax, 10);
        $totalReturnedTaxInclusive = BC::sub($totalBeforeInvoiceDiscount, $totalInvoiceDiscount, 10);
        $totalReturnedTaxInclusive = BC::round($totalReturnedTaxInclusive, 2);

        // Use the correctly calculated tax (Product VAT + Row Discount VAT)
        $totalTaxRounded = BC::round($totalTax, 2);
        // Calculate tax_exclusive as the difference to maintain: tax_inclusive = tax_exclusive + tax
        $totalReturnedTaxExclusive = BC::sub($totalReturnedTaxInclusive, $totalTaxRounded, 2);

        return [
            "total_tax_inclusive" => $totalReturnedTaxInclusive,
            "total_tax_exclusive" => $totalReturnedTaxExclusive,
            "total_tax" => $totalTaxRounded,
            "total_cost" => BC::round($totalReturnedCost, 2),
            "total_cost_tax_exclusive" => BC::round($totalReturnedCostTaxExclusive, 2),
            "invoice_type" => InvoiceTypeEnum::RETURN->value,
            "invoice_type_performed" => InvoiceTypePerformedEnum::RETURN->value,
            "user_id" => $user->id,
            "zatca_date" => $zatca_date,
            "created_at" => date("Y-m-d H:i:s"),
            "updated_at" => date("Y-m-d H:i:s"),
            "qr_zatca" => $qr_zatca,
            "qr_zatca_image" => $qr_zatca_image,
            'branch_id' => $branch,
            'registery_id' => $productsWithMoreInformation->first()['registery_id'],
            'customer_id' => $productsWithMoreInformation->first()['customer_id'],
            "pretty_id" => "R-" . substr(MerchantInvoice::where('id', $invoiceId)->first()['pretty_id'], 2) . '-' . $returnInvoiceCount,
            "related_invoice" => $invoiceId,
            'wholesale_status' => $productsWithMoreInformation->first()['wholesale_status'],
            'is_multipay' => 0,
            'has_postpay' => 0,
            'is_discounted' => 1,
            'tax_code' => $insertProducts[0]['tax_code'],
            "tax_schema" => $insertProducts[0]["tax_schema"],
            'zatca_uuid' => Str::uuid(),
            'zatca_related_invoice' => $originalInvoice->zatca_sequence,
            'zatca_invoice_name' => $originalInvoice->zatca_invoice_name,
            'return_reason' => ZatcaReturnInvoiceReasons::GOODS_OR_SERVICES_REFUND->value
        ];
    }

    /**
     * Prepare invoice data for a regular (non-discounted) invoice
     */
    private function prepareRegularInvoiceData($totalAmountReturned, $totalTaxReturned, $totalCost, $totalCostTaxExclusive, $zatca_date, $qr_zatca, $qr_zatca_image, $branch, $productsWithMoreInformation, $returnInvoiceCount, $invoiceId, $insertProducts, $originalInvoice, $user)
    {
        // Round the primary values first, then calculate tax_exclusive to ensure consistency
        $totalTaxInclusive = BC::round($totalAmountReturned, 2);
        $totalTax = BC::round($totalTaxReturned, 2);
        // Calculate tax_exclusive as the difference to maintain: tax_inclusive = tax_exclusive + tax
        $totalTaxExclusive = BC::sub($totalTaxInclusive, $totalTax, 2);

        return [
            "total_tax_inclusive" => $totalTaxInclusive,
            "total_tax_exclusive" => $totalTaxExclusive,
            "total_tax" => $totalTax,
            "total_cost" => BC::round($totalCost, 2),
            "total_cost_tax_exclusive" => BC::round($totalCostTaxExclusive, 2),
            "invoice_type" => 'return',
            "invoice_type_performed" => InvoiceTypePerformedEnum::RETURN->value,
            "user_id" => $user->id,
            "zatca_date" => $zatca_date,
            "created_at" => date("Y-m-d H:i:s"),
            "updated_at" => date("Y-m-d H:i:s"),
            "qr_zatca" => $qr_zatca,
            "qr_zatca_image" => $qr_zatca_image,
            'branch_id' => $branch,
            'registery_id' => $productsWithMoreInformation->first()['registery_id'],
            'customer_id' => $productsWithMoreInformation->first()['customer_id'],
            "pretty_id" => "R-" . substr(MerchantInvoice::where('id', $invoiceId)->first()['pretty_id'], 2) . '-' . $returnInvoiceCount,
            "related_invoice" => $invoiceId,
            'wholesale_status' => $productsWithMoreInformation->first()['wholesale_status'],
            'is_multipay' => 0,
            'has_postpay' => 0,
            'is_discounted' => 0,
            'tax_code' => $insertProducts[0]['tax_code'],
            "tax_schema" => $insertProducts[0]["tax_schema"],
            'zatca_uuid' => Str::uuid(),
            'zatca_related_invoice' => $originalInvoice->zatca_sequence,
            'zatca_invoice_name' => $originalInvoice->zatca_invoice_name,
            'return_reason' => ZatcaReturnInvoiceReasons::GOODS_OR_SERVICES_REFUND->value
        ];
    }

    /**
     * Create a discount record for a return invoice
     * Always calculate from sum of individual product discounts
     */
    private function createReturnDiscount($invoiceId, $originalInvoice, $insertProducts)
    {
        // Calculate discount amounts from sum of individual products
        $totalReturnedDiscountTaxExclusive = '0';
        $totalReturnedDiscountTax = '0';

        foreach ($insertProducts as $product) {
            // Sum discount_tax_exclusive from all returned products
            if (isset($product['row_invoice_discount']) && $product['row_invoice_discount'] > 0) {
                $totalReturnedDiscountTaxExclusive = BC::add($totalReturnedDiscountTaxExclusive, $product['row_invoice_discount'], 10);
            }
            // Sum discount_tax from all returned products
            if (isset($product['row_invoice_discount_vat']) && $product['row_invoice_discount_vat'] > 0) {
                $totalReturnedDiscountTax = BC::add($totalReturnedDiscountTax, $product['row_invoice_discount_vat'], 10);
            }
        }

        // Calculate total after discount: sum product totals then subtract invoice discounts
        $totalBeforeDiscount = '0';
        foreach ($insertProducts as $product) {
            $totalBeforeDiscount = BC::add($totalBeforeDiscount, $product['total'], 10);
        }
        $totalInvoiceDiscount = BC::add($totalReturnedDiscountTaxExclusive, $totalReturnedDiscountTax, 10);
        $totalAfterDiscount = BC::sub($totalBeforeDiscount, $totalInvoiceDiscount, 10);
        $totalAfterDiscount = BC::round($totalAfterDiscount, 2);

        // Create the invoice-level discount record
        MerchantSaleInvoiceDiscount::create([
            'invoice_id' => $invoiceId,
            'type' => 'return',
            'total_after_discount' => $totalAfterDiscount,
            'total_discount_tax_exclusive' => $totalReturnedDiscountTaxExclusive,
            'total_discount_tax_exclusive_rounded' => BC::round($totalReturnedDiscountTaxExclusive, 2),
            'total_discount_tax' => $totalReturnedDiscountTax,
            'total_discount_tax_rounded' => BC::round($totalReturnedDiscountTax, 2),
        ]);
    }

    /**
     * Create a payment method record for a return invoice
     */
    private function createReturnPaymentMethod($invoiceId, $totalAmountReturned, $paymentId)
    {
        MerchantSaleInvoicePaymentMethod::create([
            'invoice_id' => $invoiceId,
            'invoice_type' => 'return',
            'invoice_type_performed' => InvoiceTypePerformedEnum::RETURN->value,
            'amount' => "-$totalAmountReturned",
            'payment_id' => $paymentId,
            'is_postpay' => 0,
        ]);
    }

    /**
     * Update inventory quantities for returned products
     */
    private function updateInventoryQuantities($product)
    {
        // Handle variant product return
        if ($product['type'] === ProductTypeEnum::VARIANT->value) {
            $variantOptionId = $product['product_variant_id'] ?? null;

            if ($variantOptionId) {
                $variantBranchQuantity = MerchantProductBranchQuantity::where('product_variant_id', $variantOptionId)
                    ->where('branch_id', $product['branch_id'])
                    ->first();

                if ($variantBranchQuantity) {
                    $variantBranchQuantity->quantity += $product['cart_quantity'];
                    $variantBranchQuantity->save();
                }
            }
        } elseif ($product['type'] === ProductTypeEnum::COMPOSITE->value) {
            // Handle composite product return - increase component quantities
            $compositeProduct = MerchantsProduct::find($product['product_id']);
            $components = $compositeProduct->compositeComponents;

            foreach ($components as $component) {
                $componentProduct = $component->componentProduct;
                $quantityToAdd = $component->quantity * $product['cart_quantity'];

                $currentValue = MerchantProductBranchQuantity::query()
                    ->where([
                        'product_id' => $componentProduct->id,
                        'branch_id' => $product['branch_id'],
                    ])
                    ->value('quantity');

                $updatedValue = $currentValue + $quantityToAdd;

                MerchantProductBranchQuantity::query()
                    ->where([
                        'product_id' => $componentProduct->id,
                        'branch_id' => $product['branch_id']
                    ])
                    ->update(['quantity' => $updatedValue]);
            }
        } else {
            // Handle regular product return
            $currentValue = MerchantProductBranchQuantity::query()
                ->where([
                    'product_id' => $product['product_id'],
                    'branch_id' => $product['branch_id'],
                ])
                ->first()
                ->quantity;

            $updatedValue = $currentValue + $product['cart_quantity'];

            MerchantProductBranchQuantity::query()
                ->where([
                    'product_id' => $product['product_id'],
                    'branch_id' => $product['branch_id']
                ])
                ->update(['quantity' => $updatedValue]);
        }
    }

    /**
     * Validate promotion group returns
     *
     * @param MerchantInvoice $invoice
     * @param array $returnedItems
     * @return string|null Error message or null if valid
     */
    private function validatePromotionGroupReturns(MerchantInvoice $invoice, array $returnedItems): ?string
    {
        // Get promotion groups for this invoice
        $promotionGroups = MerchantSaleInvoicePromotionGroup::where('invoice_id', $invoice->id)->get();

        if ($promotionGroups->isEmpty()) {
            return null; // No promotion groups to validate
        }

        // Check each promotion group, but only validate groups that have products being returned
        foreach ($promotionGroups as $group) {
            // First check if any products from this group are being returned
            $groupProducts = MerchantSaleInvoiceProduct::where('invoice_id', $invoice->id)
                ->where('promotion_group_id', $group->group_id)
                ->get();

            $hasReturnsInGroup = false;
            foreach ($groupProducts as $product) {
                $productKey = $this->constructProductKey($product);
                if (isset($returnedItems[$productKey]) && $returnedItems[$productKey] > 0) {
                    $hasReturnsInGroup = true;
                    break;
                }
            }

            // Only validate if this group has products being returned
            if ($hasReturnsInGroup) {
                $error = $this->validateSinglePromotionGroup($group, $invoice, $returnedItems);
                if ($error) {
                    return $error;
                }
            }
        }

        return null; // All valid
    }

    /**
     * Validate a single promotion group
     *
     * @param MerchantSaleInvoicePromotionGroup $group
     * @param MerchantInvoice $invoice
     * @param array $returnedItems
     * @return string|null Error message or null if valid
     */
    private function validateSinglePromotionGroup($group, $invoice, $returnedItems): ?string
    {
        // Get products in this group
        $groupProducts = MerchantSaleInvoiceProduct::where('invoice_id', $invoice->id)
            ->where('promotion_group_id', $group->group_id)
            ->get();

        $buyXReturned = 0;
        $getYReturned = 0;
        $buyXTotal = 0;
        $getYTotal = 0;
        $getYRemaining = 0;

        // Calculate what's being returned from this group and what's available
        foreach ($groupProducts as $product) {
            // Use consistent product key construction
            $productKey = $this->constructProductKey($product);
            $returnQuantity = $returnedItems[$productKey] ?? 0;

            if ($product->promotion_product_type === 'buy_x') {
                $buyXReturned += $returnQuantity;
                $buyXTotal += $product->cart_quantity;
            } elseif ($product->promotion_product_type === 'get_y') {
                $getYReturned += $returnQuantity;
                $getYTotal += $product->cart_quantity;
            }
        }

        // Calculate remaining get_y products available for return
        // We need to check how many get_y products have already been returned
        $alreadyReturnedGetY = 0;
        $returnInvoices = MerchantSaleInvoiceProduct::where('invoice_id', '!=', $invoice->id)
            ->where('related_invoice', $invoice->id)
            ->where('promotion_group_id', $group->group_id)
            ->where('promotion_product_type', 'get_y')
            ->get();

        foreach ($returnInvoices as $returnedProduct) {
            $alreadyReturnedGetY += $returnedProduct->cart_quantity;
        }

        $getYRemaining = $getYTotal - $alreadyReturnedGetY;

        // If nothing from this group is being returned, skip
        if ($buyXReturned === 0 && $getYReturned === 0) {
            return null;
        }

        // Business Rules:
        $buyXRequired = $group->buy_x_quantity_required;
        $getYGiven = $group->get_y_quantity_given;
        $promotionName = $group->getDescription();

        // Rule 1: Must return complete groups (buy_x must be divisible by required amount)
        if ($buyXReturned % $buyXRequired !== 0) {
            return "يجب إرجاع مجموعات العرض كاملة. العرض '{$promotionName}' يتطلب إرجاع {$buyXRequired} منتجات معاً.";
        }

        // Rule 2: Must return matching get_y products (but only if they exist in the group)
        $expectedGetY = ($buyXReturned / $buyXRequired) * $getYGiven;

        // Calculate the actual available get_y products that should be returned
        // This accounts for cases where not all theoretical get_y products were actually sold
        $actualExpectedGetY = min($expectedGetY, $getYTotal);

        if ($getYReturned !== $actualExpectedGetY) {
            if ($actualExpectedGetY === 0) {
                // No get_y products available to return, so allow buy_x only return
                return null;
            }
            return "يجب إرجاع {$actualExpectedGetY} من المنتجات المجانية مع {$buyXReturned} من المنتجات المشتراة للعرض '{$promotionName}'.";
        }

        // Rule 3: Cannot return only get_y without buy_x (but allow buy_x without get_y if no get_y exists)
        if ($getYReturned > 0 && $buyXReturned === 0) {
            return "لا يمكن إرجاع المنتجات المجانية بدون إرجاع المنتجات المشتراة المرتبطة بها في العرض '{$promotionName}'.";
        }

        return null; // Valid
    }

    /**
     * Get promotion groups summary for an invoice
     *
     * @param int $invoiceId
     * @return array
     */
    public function getPromotionGroupsSummary(int $invoiceId): array
    {
        $invoice = MerchantInvoice::find($invoiceId);
        if (!$invoice) {
            return [];
        }

        $promotionGroups = MerchantSaleInvoicePromotionGroup::where('invoice_id', $invoice->id)->get();
        $summary = [];

        foreach ($promotionGroups as $group) {
            $groupProducts = MerchantSaleInvoiceProduct::where('invoice_id', $invoice->id)
                ->where('promotion_group_id', $group->group_id)
                ->get();

            $summary[] = [
                'group_id' => $group->group_id,
                'description' => $group->getDescription(),
                'buy_x_quantity_required' => $group->buy_x_quantity_required,
                'get_y_quantity_given' => $group->get_y_quantity_given,
                'products' => $groupProducts->map(function ($product) {
                    return [
                        'product_id' => $product->product_id,
                        'product_name' => $product->product_name_ar,
                        'quantity' => $product->cart_quantity,
                        'type' => $product->promotion_product_type
                    ];
                })
            ];
        }

        return $summary;
    }

    /**
     * Check if an invoice has promotion groups
     *
     * @param int $invoiceId
     * @return bool
     */
    public function hasPromotionGroups(int $invoiceId): bool
    {
        return MerchantSaleInvoicePromotionGroup::where('invoice_id', $invoiceId)->exists();
    }



    /**
     * Construct product key consistently across the service
     *
     * @param object $product
     * @return string
     */
    private function constructProductKey($product): string
    {
        $productKey = $product->product_id;

        // Add variant_id if it's a variant product
        if (isset($product->is_variant) && $product->is_variant && $product->product_variant_id) {
            $productKey .= '-' . $product->product_variant_id;
        }

        // Add promotion_id if it has promotion to match the cart ID format
        // This ensures promotion products are distinguished from regular products
        if (isset($product->has_promotion) && $product->has_promotion && $product->promotion_id) {
            $productKey .= '-' . $product->promotion_id;
        }

        return $productKey;
    }
}
