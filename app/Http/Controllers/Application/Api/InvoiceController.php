<?php

namespace App\Http\Controllers\Application\Api;

use App\Actions\Application\Api\CreateInvoiceAction;
use App\Exceptions\LogicalException;
use App\Http\Controllers\Application\Web\Controller;
use App\Http\Requests\Application\Api\CreateCartRequest;
use App\Http\Requests\Application\Api\CreateInvoiceRequest;
use App\Http\Resources\Application\Branch\PaymentTypesResource;
use App\Http\Resources\Application\Branch\SettingResource;
use App\Http\Resources\Application\Cart\CartResource;
use App\Http\Resources\Application\Cart\CartsResource;
use App\Http\Resources\Application\Category\CategoriesResource;
use App\Http\Resources\Application\Category\CategoryResource;
use App\Http\Resources\Application\Branch\BranchesResource;
use App\Http\Resources\Application\Product\ProductResource;
use App\Http\Resources\Application\Product\ProductsResource;
use App\Http\Resources\Platform\Facilities\Interviews\InterviewsResource;
use App\Models\MerchantBranch;
use App\Models\MerchantCategory;
use App\Models\MerchantPaymentMethod;
use App\Models\MerchantPaymentMethodRegister;
use App\Models\MerchantRegisterSetting;
use App\Models\MerchantRegistery;
use App\Models\MerchantTempInvoice;
use Barryvdh\DomPDF\Facade\Pdf;
use Illuminate\Http\JsonResponse;


class InvoiceController extends Controller
{
    /**
     * @throws LogicalException
     */
    public function store(MerchantBranch $branch, MerchantRegistery $register, CreateInvoiceRequest $request): JsonResponse
    {

        $pdfContent = app(CreateInvoiceAction::class)($branch, $register, $request);

        $base64Pdf = base64_encode($pdfContent->output());

        return sendSuccessResponse('Invoice created successfully',
            [
                'pdf' => $base64Pdf,
                'filename' => 'invoice.pdf'
            ],
        );
    }
}
